package storage

import (
    "database/sql"
    "fmt"
    "io/ioutil"
    "os"
    "time"

    _ "github.com/lib/pq"
)

type DocumentMetadata struct {
    ID        string
    Filename  string
    CaseNumber string
    Party string
    Type string
    Status string
    Uploaded  time.Time
    Encrypted bool
}

var db *sql.DB

func InitPostgres(conn string) error {
    var err error
    db, err = sql.Open("postgres", conn)
    return err
}

func StoreEncryptedFile(filePath string, meta DocumentMetadata) error {
    data, _ := ioutil.ReadFile(filePath)
    _, err := db.Exec(\`INSERT INTO documents (id, filename, uploaded, encrypted_data)
                       VALUES ($1,$2,$3,$4)\`,
        meta.ID, meta.Filename, meta.Uploaded, data)
    return err
}

func RetrieveEncryptedFile(docID string) (string, string, error) {
    var data []byte
    var filename string
    row := db.QueryRow(\`SELECT filename, encrypted_data FROM documents WHERE id=$1\`, docID)
    if err := row.Scan(&filename, &data); err != nil {
        return "", "", err
    }
    tmpPath := "/tmp/" + docID + ".pgp"
    ioutil.WriteFile(tmpPath, data, 0600)
    return tmpPath, filename, nil
}

func CleanupTemp(path string) {
    os.Remove(path)
}
