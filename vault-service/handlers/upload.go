package handlers

import (
    "fmt"
    "io"
    "net/http"
    "os"
    "time"
    "vault/pgp"
    "vault/storage"

    "github.com/google/uuid"
)

func UploadHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodPost {
        http.Error(w, "Only POST allowed", http.StatusMethodNotAllowed)
        return
    }

    file, header, err := r.FormFile("file")
    if err != nil {
        http.Error(w, "File upload error: "+err.Error(), http.StatusBadRequest)
        return
    }
    defer file.Close()

    docID := uuid.New().String()
    tmpPath := "/tmp/" + docID + "_" + header.Filename
    out, _ := os.Create(tmpPath)
    defer out.Close()
    io.Copy(out, file)

    encPath := tmpPath + ".pgp"
    if err := pgp.EncryptFile(tmpPath, encPath); err != nil {
        http.Error(w, "Encryption error: "+err.Error(), http.StatusInternalServerError)
        return
    }
    os.Remove(tmpPath)

    meta := storage.DocumentMetadata{
        ID:        docID,
        Filename:  header.Filename,
        Uploaded:  time.Now(),
        Encrypted: true,
    }

    if err := storage.StoreEncryptedFile(encPath, meta); err != nil {
        http.Error(w, "Storage error: "+err.Error(), http.StatusInternalServerError)
        return
    }
    os.Remove(encPath)

    storage.CacheMetadata(meta)

    w.Header().Set("Content-Type", "application/json")
    fmt.Fprintf(w, `{"doc_id":"%s","message":"File uploaded & encrypted"}`, docID)
}
