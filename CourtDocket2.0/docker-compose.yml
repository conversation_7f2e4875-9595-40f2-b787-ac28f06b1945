version: '3.9'

services:
  vault:
    build: ./vault-service
    container_name: go_vault_service
    environment:
      - POSTGRES_CONN=***************************************/vaultdb?sslmode=disable
      - REDIS_ADDR=redis:6379
    ports:
      - "8080:8080"
    volumes:
      - ./keys:/app/keys
      - ./data:/data
    depends_on:
      - postgres
      - redis

  ai-brain:
    build: ./ai-brain
    container_name: ai_brain_service
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - VAULT_URL=http://vault:8080
    ports:
      - "8001:8000"
    depends_on:
      - vault
      - qdrant

  postgres:
    image: postgres:15-alpine
    container_name: vault_postgres
    environment:
      POSTGRES_USER: vault
      POSTGRES_PASSWORD: password
      POSTGRES_DB: vaultdb
    ports:
      - "5433:5432"
    volumes:
      - ./pgdata:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    container_name: vault_redis
    ports:
      - "6380:6379"

  qdrant:
    image: qdrant/qdrant
    container_name: vault_qdrant
    ports:
      - "6333:6333"
    volumes:
      - ./qdrant_storage:/qdrant/storage
