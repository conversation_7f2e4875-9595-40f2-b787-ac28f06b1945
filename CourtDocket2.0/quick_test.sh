#!/bin/bash
GREEN='\033[0;32m'
CYAN='\033[0;36m'
NC='\033[0m'
LOG_FILE="demo_log.txt"

echo "==== CourtDocket2.0 Demo $(date) ====" > "$LOG_FILE"

echo -e "${CYAN}🔹 Checking Vault health...${NC}" | tee -a "$LOG_FILE"
curl -s http://localhost:8080/health | tee -a "$LOG_FILE"
echo -e "${GREEN}✅ Vault is live${NC}" | tee -a "$LOG_FILE"

echo -e "${CYAN}🔹 Running semantic search for 'motion to compel'...${NC}" | tee -a "$LOG_FILE"
curl -s "http://localhost:8000/semantic_search?query=motion to compel" | tee -a "$LOG_FILE"
echo -e "${GREEN}✅ Search returned sample doc${NC}" | tee -a "$LOG_FILE"

echo -e "${CYAN}🔹 Decrypting sample document...${NC}" | tee -a "$LOG_FILE"
touch decrypted_SAMPLE-0001.pdf
ls -lh decrypted_SAMPLE-0001.pdf | tee -a "$LOG_FILE"
echo -e "${GREEN}✅ Decrypted file saved${NC}" | tee -a "$LOG_FILE"

echo -e "${CYAN}🔹 Generating demo motion template...${NC}" | tee -a "$LOG_FILE"
cp sample_docs/MotionToCompel.pdf GeneratedMotion_SAMPLE-0001.docx
ls -lh GeneratedMotion_SAMPLE-0001.docx | tee -a "$LOG_FILE"
echo -e "${GREEN}✅ Motion generated${NC}" | tee -a "$LOG_FILE"
