package handlers

import (
    "encoding/json"
    "net/http"
)

type SearchResult struct {
    ID       string `json:"id"`
    Filename string `json:"filename"`
    Type     string `json:"type"`
    Status   string `json:"status"`
}

func SearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    query := r.URL.Query().Get("q")
    if query == "" {
        http.Error(w, "Query parameter 'q' is required", http.StatusBadRequest)
        return
    }

    // For now, return a simple mock response
    // In a real implementation, this would search the database
    results := []SearchResult{
        {
            ID:       "sample-1",
            Filename: "sample-document.pdf",
            Type:     "legal-document",
            Status:   "encrypted",
        },
    }

    w.<PERSON><PERSON>().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "query":   query,
        "results": results,
    })
}
