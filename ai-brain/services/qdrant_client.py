from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.models import VectorParams, Distance, PointStruct
import os

QDRANT_HOST = os.getenv("QDRANT_HOST", "qdrant")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", 6333))
COLLECTION = "vault_embeddings"

client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

# Ensure collection exists
client.recreate_collection(
    collection_name=COLLECTION,
    vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
)

def upsert_embedding(doc_id: str, vector: list[float], payload: dict):
    client.upsert(
        collection_name=COLLECTION,
        points=[PointStruct(id=doc_id, vector=vector, payload=payload)]
    )

def semantic_search(vector: list[float], limit: int = 5):
    hits = client.search(collection_name=COLLECTION, query_vector=vector, limit=limit)
    return [{"doc_id": h.id, "score": h.score, "payload": h.payload} for h in hits]
