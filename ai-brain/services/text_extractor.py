import fitz  # PyMuPDF
import pytesseract
from PIL import Image
import io

def extract_text(file_path: str) -> str:
    doc = fitz.open(file_path)
    text = ""
    for page in doc:
        txt = page.get_text()
        if not txt.strip():
            pix = page.get_pixmap()
            img = Image.open(io.BytesIO(pix.tobytes("png")))
            txt = pytesseract.image_to_string(img)
        text += txt + "\n"
    return text
