from fastapi import FastAPI, UploadFile
from services import vault_client, text_extractor, embeddings, qdrant_client
import uuid

app = FastAPI(title="AI Brain for Legal Vault")

@app.post("/process")
async def process_document(file: UploadFile):
    file_path = f"/tmp/{uuid.uuid4()}_{file.filename}"
    with open(file_path, "wb") as f:
        f.write(await file.read())

    text = text_extractor.extract_text(file_path)
    vector = embeddings.get_embedding(text)

    doc_id = str(uuid.uuid4())
    qdrant_client.upsert_embedding(doc_id, vector, {
        "filename": file.filename,
        "text_preview": text[:200]
    })

    return {"doc_id": doc_id, "message": "Document processed and embedded."}

@app.get("/semantic_search")
async def semantic_search(query: str):
    vector = embeddings.get_embedding(query)
    results = qdrant_client.semantic_search(vector)
    return results
