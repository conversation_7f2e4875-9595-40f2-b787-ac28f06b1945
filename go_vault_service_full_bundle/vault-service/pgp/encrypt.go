package pgp

import (
    "os"
    "io"
    "github.com/ProtonMail/go-crypto/openpgp"
    "github.com/ProtonMail/go-crypto/openpgp/armor"
)

func EncryptFile(inputPath, outputPath string) error {
    pubKeyFile := "keys/public.asc"
    keyFile, err := os.Open(pubKeyFile)
    if err != nil {
        return err
    }
    defer keyFile.Close()

    entityList, err := openpgp.ReadArmoredKeyRing(keyFile)
    if err != nil {
        return err
    }

    inFile, err := os.Open(inputPath)
    if err != nil {
        return err
    }
    defer inFile.Close()

    outFile, err := os.Create(outputPath)
    if err != nil {
        return err
    }
    defer outFile.Close()

    w, err := armor.Encode(outFile, "PGP MESSAGE", nil)
    if err != nil {
        return err
    }

    plainWriter, err := openpgp.Encrypt(w, entityList, nil, nil, nil)
    if err != nil {
        return err
    }

    if _, err := io.Copy(plainWriter, inFile); err != nil {
        return err
    }

    plainWriter.Close()
    w.Close()

    return nil
}
