package pgp

import (
    "os"
    "io"
    "github.com/ProtonMail/go-crypto/openpgp"
    "github.com/ProtonMail/go-crypto/openpgp/armor"
)

func DecryptFile(inputPath, outputPath string) error {
    privKeyFile := "keys/private.asc"
    keyFile, err := os.Open(privKeyFile)
    if err != nil {
        return err
    }
    defer keyFile.Close()

    entityList, err := openpgp.ReadArmoredKeyRing(keyFile)
    if err != nil {
        return err
    }

    inFile, err := os.Open(inputPath)
    if err != nil {
        return err
    }
    defer inFile.Close()

    block, err := armor.Decode(inFile)
    if err != nil {
        return err
    }

    md, err := openpgp.ReadMessage(block.Body, entityList, nil, nil)
    if err != nil {
        return err
    }

    outFile, err := os.Create(outputPath)
    if err != nil {
        return err
    }
    defer outFile.Close()

    _, err = io.Copy(outFile, md.UnverifiedBody)
    return err
}
