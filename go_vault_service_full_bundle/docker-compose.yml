version: '3.8'
services:
  vault:
    build: ./vault-service
    container_name: go_vault_service
    environment:
      - POSTGRES_CONN=***************************************/vaultdb?sslmode=disable
      - REDIS_ADDR=redis:6379
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
      - qdrant
    volumes:
      - ./keys:/app/keys
      - ./data:/data

  postgres:
    image: postgres:15-alpine
    container_name: vault_postgres
    environment:
      POSTGRES_USER: vault
      POSTGRES_PASSWORD: password
      POSTGRES_DB: vaultdb
    ports:
      - "5432:5432"
    volumes:
      - ./pgdata:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    container_name: vault_redis
    ports:
      - "6379:6379"

  qdrant:
    image: qdrant/qdrant
    container_name: vault_qdrant
    ports:
      - "6333:6333"
    volumes:
      - ./qdrant_storage:/qdrant/storage
